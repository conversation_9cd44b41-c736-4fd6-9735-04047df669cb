sfHost = samsonrope.my.salesforce.com
sfHostCustomer = samsonrope.my.site.com/connect

client_id = 3MVG9ytVT1SanXDlzy6isqr7._5ySmOMjM0xYoKVIlNZwcGMyiXU_bNo0qYy7mOi7YXV8M7skhpf9HD04DD.C
client_secret = ****************************************************************

umpUrl = http://127.0.0.1:8080/UMP
sa_password = nm6200fs9upgzi7uwp65ctwt2jr4o0z8c3fvfgds
user_nonce = ca1d1efb-8f0d-409a-a3f2-8b3bab175602

company = SAMSON
applicationName = ROPE_INSPECTIONS
backendPort = SAMSON_SFDC_PORT
deviceRedirectUrl = samson://
sfRedirect = https://live.unvired.io/auth/callback
webappRedirectUrl = app.samsonrope.net

#sfHost = samsonrope--sambox.cs40.my.salesforce.com
#client_id = 3MVG9Z8h6Bxz0zc4hj178ZJUVeQuFDARB_qWB4pJVIxojVBNJDkLcZgtUxZSl1FhaepJffmPvZhi3Kj5MLDZW
#client_secret = ****************************************************************

#umpUrl = https://sandbox.unvired.io/UMP
#sa_password = iv2d9tsodjz82qkz55dzh2nscja29bo2sny1c5wb

#umpUrl = http://**************:8080/UMP
#sa_password = unvired

#company = samson
#applicationName = ROPE_INSPECTIONS
#backendPort = SAMSON_SFDC_PORT
#deviceRedirectUrl = samson://
#sfRedirect = https://samson.unvired.io/auth/callback







##sfHost = samsonrope--SamBox.my.salesforce.com
#sfHost = samsonrope--sambox.sandbox.my.salesforce.com
#sfHostCustomer = samsonrope--sambox.sandbox.my.site.com/connect
#
##Production
##client_id = 3MVG9Z8h6Bxz0zc4hj178ZJUVeQuFDARB_qWB4pJVIxojVBNJDkLcZgtUxZSl1FhaepJffmPvZhi3Kj5MLDZW
##client_secret = ****************************************************************
#
##Sandbox
#client_id = 3MVG9E8TNx7FN9y4YUwX8eaocyjKrfvjz4pYx45NPP_6j_DETsHtf86NKx8WIfTDzke7csG4tbs0VJ0TJDtsV
#client_secret = ****************************************************************
#
##Sandbox
##umpUrl = http://127.0.0.1:8100/UMP
##sa_password = iv2d9tsodjz82qkz55dzh2nscja29bo2sny1c5wb
#
##Sandbox
#umpUrl = https://sandbox.unvired.io/UMP
#sa_password = iv2d9tsodjz82qkz55dzh2nscja29bo2sny1c5wb
#
##160
##umpUrl = https://umpdev.unvired.io/UMP
##sa_password = unvired
#
##Sandbox
#user_nonce = 784b45c3-f890-4f21-95f6-cf1bf0b9b459
#
##Production
##user_nonce = ca1d1efb-8f0d-409a-a3f2-8b3bab175602
#
#company = SAMSON
#applicationName = ROPE_INSPECTIONS
#backendPort = SAMSON_SFDC_PORT
#deviceRedirectUrl = samson://
#sfRedirect = https://samson.unvired.io/auth/callback
##sfRedirect = https://0613-***********-63.in.ngrok.io/auth/callback
#
##Sandbox
#webappRedirectUrl = app.sambox.samsonrope.net
##Production
##webappRedirectUrl = app.samsonrope.net
#
#
##sfRedirect = https://umpdev.unvired.io/auth/callback
##sfRedirect = http://localhost:8080/auth/callback
##sfRedirect = https://93085131b9e6.ngrok.io/auth/callback