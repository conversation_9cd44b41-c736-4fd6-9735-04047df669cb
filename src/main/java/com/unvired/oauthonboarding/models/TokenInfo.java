package com.unvired.oauthonboarding.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TokenInfo {

    @SerializedName("access_token")
    @Expose
    private String access_token;
    @SerializedName("id")
    @Expose
    private String id;
    @SerializedName("id_token")
    @Expose
    private String id_token;
    @SerializedName("instance_url")
    @Expose
    private String instance_url;
    @SerializedName("issued_at")
    @Expose
    private String issued_at;
    @SerializedName("refresh_token")
    @Expose
    private String refresh_token;
    @SerializedName("scope")
    @Expose
    private String scope;
    @SerializedName("sfdc_community_id")
    @Expose
    private String sfdc_community_id;
    @SerializedName("sfdc_community_url")
    @Expose
    private String sfdc_community_url;
    @SerializedName("signature")
    @Expose
    private String signature;
    @SerializedName("token_type")
    @Expose
    private String token_type;

    public String getAccess_token() {
        return access_token;
    }

    public void setAccessToken(String accessToken) {
        this.access_token = accessToken;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdToken() {
        return id_token;
    }

    public void setIdToken(String id_token) {
        this.id_token = id_token;
    }

    public String getInstanceUrl() {
        return instance_url;
    }

    public void setInstanceUrl(String instanceUrl) {
        this.instance_url = instanceUrl;
    }

    public String getIssuedAt() {
        return issued_at;
    }

    public void setIssuedAt(String issued_at) {
        this.issued_at = issued_at;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefreshToken(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getSfdcCommunityId() {
        return sfdc_community_id;
    }

    public void setSfdcCommunityId(String sfdc_community_id) {
        this.sfdc_community_id = sfdc_community_id;
    }

    public String getSfdcCommunityUrl() {
        return sfdc_community_url;
    }

    public void setSfdcCommunityUrl(String sfdc_community_url) {
        this.sfdc_community_url = sfdc_community_url;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getTokenType() {
        return token_type;
    }

    public void setTokenType(String token_type) {
        this.token_type = token_type;
    }
}
