package com.unvired.oauthonboarding.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UMPUser {

    private String userName;
    private String firstName;
    private String lastName;
    private String email;
    private String password;

    private String status;

    private Map[] backendUsers;
    private Map[] frontendUsers;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Map[] getBackendUsers() {
        return backendUsers;
    }

    public void setBackendUsers(Map[] backendUsers) {
        this.backendUsers = backendUsers;
    }

    public Map[] getFrontendUsers() {
        return frontendUsers;
    }

    public void setFrontendUsers(Map[] frontendUsers) {
        this.frontendUsers = frontendUsers;
    }

}