package com.unvired.oauthonboarding.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesforceUser {
    private String first_name;
    private String last_name;
    private String username;
    private String email;

    public String getFirst_name() {
        return first_name;
    }

    public void setFirst_name(String first_name) {
        this.first_name = first_name;
    }

    public String getLast_name() {
        return last_name;
    }

    public void setLast_name(String last_name) {
        this.last_name = last_name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

}
/*
{
        "id": "https://login.salesforce.com/id/00D90000000bBFpEAM/00590000000piGTAAY",
        "asserted_user": true,
        "user_id": "00590000000piGTAAY",
        "organization_id": "00D90000000bBFpEAM",
        "username": "<EMAIL>",
        "nick_name": "pdmca811.3213419484877925E12",
        "display_name": "<PERSON>",
        "email": "<EMAIL>",
        "email_verified": true,
        "first_name": "Dan",
        "last_name": "Hailey",
        "timezone": "America/Los_Angeles",
        "photos": {
        "picture": "https://c.ap8.content.force.com/profilephoto/005/F",
        "thumbnail": "https://c.ap8.content.force.com/profilephoto/005/T"
        },
        "addr_street": null,
        "addr_city": "Houston",
        "addr_state": "Texas",
        "addr_country": "USA",
        "addr_zip": "77059",
        "mobile_phone": null,
        "mobile_phone_verified": false,
        "is_lightning_login_user": false,
        "status": {
        "created_date": null,
        "body": null
        },
        "urls": {
        "enterprise": "https://ap8.salesforce.com/services/Soap/c/{version}/00D90000000bBFp",
        "metadata": "https://ap8.salesforce.com/services/Soap/m/{version}/00D90000000bBFp",
        "partner": "https://ap8.salesforce.com/services/Soap/u/{version}/00D90000000bBFp",
        "rest": "https://ap8.salesforce.com/services/data/v{version}/",
        "sobjects": "https://ap8.salesforce.com/services/data/v{version}/sobjects/",
        "search": "https://ap8.salesforce.com/services/data/v{version}/search/",
        "query": "https://ap8.salesforce.com/services/data/v{version}/query/",
        "recent": "https://ap8.salesforce.com/services/data/v{version}/recent/",
        "tooling_soap": "https://ap8.salesforce.com/services/Soap/T/{version}/00D90000000bBFp",
        "tooling_rest": "https://ap8.salesforce.com/services/data/v{version}/tooling/",
        "profile": "https://ap8.salesforce.com/00590000000piGTAAY",
        "feeds": "https://ap8.salesforce.com/services/data/v{version}/chatter/feeds",
        "groups": "https://ap8.salesforce.com/services/data/v{version}/chatter/groups",
        "users": "https://ap8.salesforce.com/services/data/v{version}/chatter/users",
        "feed_items": "https://ap8.salesforce.com/services/data/v{version}/chatter/feed-items",
        "feed_elements": "https://ap8.salesforce.com/services/data/v{version}/chatter/feed-elements",
        "custom_domain": "https://unviredtest-dev-ed.my.salesforce.com"
        },
        "active": true,
        "user_type": "STANDARD",
        "language": "en_US",
        "locale": "en_US",
        "utcOffset": -28800000,
        "last_modified_date": "2019-03-27T09:45:42Z",
        "is_app_installed": true
        }

        */
