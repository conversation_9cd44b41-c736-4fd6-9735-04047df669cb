package com.unvired.oauthonboarding;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.hash.Hashing;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.unvired.oauthonboarding.helpers.Constants;
import com.unvired.oauthonboarding.helpers.URLHelper;
import com.unvired.oauthonboarding.models.SalesforceUser;
import com.unvired.oauthonboarding.models.TokenInfo;
import com.unvired.oauthonboarding.models.UMPUser;
import com.unvired.oauthonboarding.helpers.Version;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@RestController
@Slf4j
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:8100", "https://localhost:8100", "https://localhost", "ionic://localhost"})
public class OAuthController {

    // Fixed values
    private String company = "SAMSON";
    private String applicationName = "ROPE_INSPECTIONS";
    private String backendPort = "SAMSON_SFDC_PORT";

    @Value("#{systemProperties['samson.sfHost']}")
    private String sfHost;

    @Value("#{systemProperties['samson.sfHostCustomer']}")
    private String sfHostCustomer;

    @Value("#{systemProperties['samson.client_id']}")
    private String clientId;

    @Value("#{systemProperties['samson.client_secret']}")
    private String clientSecret;

    @Value("#{systemProperties['samson.umpUrl']}")
    private String umpUrl;

    @Value("#{systemProperties['samson.sa_password']}")
    private String saPassword;

    @Value("#{systemProperties['samson.deviceRedirectUrl']}")
    private String deviceRedirectUrl;

    @Value("#{systemProperties['samson.webappRedirectUrl']}")
    private String webappRedirectUrl;

    @Value("#{systemProperties['samson.sfRedirect']}")
    private String sfRedirect;

    @Value("#{systemProperties['samson.user_nonce']}")
    private String userNonce;

    @GetMapping({"/version", "auth/version"})
    public String version() {
        return Version.getVersion();
    } 

    @GetMapping({"/login", "auth/login"})
    public void method(HttpServletResponse httpServletResponse,
                       @RequestParam(value = Constants.ROLE, required = false) String role,
                       @RequestParam(value = Constants.SOURCE, required = false) String source,
                       @RequestParam(value = Constants.REDIRECT, required = false) String redirect) {

        log.info("OAuthController '/login' called");

        if (role != null)
            log.info("Role: " + role);

        log.info("Redirecting to SalesForce login page");
        log.info("Url : " + (role != null ? sfHostCustomer : sfHost));

        final String state = (role != null ? Constants.CUSTOMER : Constants.EMPLOYEE)
                + Constants.DELIMITER + (source != null ? Constants.CONNECT : Constants.OAUTH)
                + Constants.DELIMITER + (redirect != null ? redirect : Constants.REDIRECT);

        final UriComponents components = UriComponentsBuilder.newInstance()
                .scheme(Constants.SCHEME)
                .host(role != null ? sfHostCustomer : sfHost)
                .path(URLHelper.sfAuthorize)
                .queryParam(Constants.RESPONSE_TYPE, Constants.CODE)
                .queryParam(Constants.CLIENT_ID, clientId)
                .queryParam(Constants.REDIRECT_URI, sfRedirect)
                .queryParam(Constants.STATE, state).build();

        httpServletResponse.setHeader(Constants.LOCATION, components.toUriString());
        httpServletResponse.setStatus(302);
    }

    @GetMapping({"/callback", "auth/callback"})
    public void tokenCallback(HttpServletResponse httpServletResponse, @RequestParam(name = Constants.CODE) String code,
                              @RequestParam(name = Constants.STATE) String state) {

        log.info("Callback from SalesForce to OAuthController '/callback'");

        log.info("State:  " + state);

        final String[] stateParams = state.split(Constants.DELIMITER);

        final String role = stateParams[0];
        final String source = stateParams[1];
        final String redirect = stateParams[2];

        log.info("State Found: Role-{} Source-{} Redirect-{}", role, source, redirect);

        final boolean isCustomer = Constants.CUSTOMER.equals(role);
        final boolean isConnect = Constants.CONNECT.equals(source);

        String error = "Something went wrong. Please try again later";

        try {
            //  Get token info from obtained code
            log.info("Getting token information");

            final TokenInfo tokenInfo = getToken(code, isCustomer);

            if (isNullOrEmpty(tokenInfo.getAccess_token())) {
                throw new Exception("Unable to get Access Token");
            }

            if (isNullOrEmpty(tokenInfo.getRefresh_token())) {
                throw new Exception("Unable to get Refresh Token");
            }

            //  Get SalesForce user information from access token
            log.info("Getting SalesForce user information");
            final SalesforceUser salesforceUser = getUserInfo(tokenInfo);

            if (isNullOrEmpty(salesforceUser.getUsername()) || isNullOrEmpty(salesforceUser.getEmail())) {
                throw new Exception("Unable to get SalesForce user detail");
            }

            //  Get UMP user details for the SalesForce User
            log.info("Getting UMP User information");
            final ResponseEntity<Map> response = checkUMPUser(salesforceUser);

            if (response == null || response.getBody() == null) {
                throw new Exception("Response/ResponseBody is null");
            }

            UMPUser umpUser;
            final Map user = (Map) response.getBody().get(Constants.USER);

            //  Create UMP user if dose not exist
            if (user == null) {
                log.info("UMP user if dose not exist");

                //  Check user_nonce to compute password
                if (userNonce == null || userNonce.isEmpty()) {
                    log.error("user_nonce value not set");
                    throw new Exception("Company configuration is incorrect, report to your Administrator");
                }

                //  Generate a guid for password
                final String guid = UUID.randomUUID().toString();

                // Post generated password GUID to UMP
                setUserGUID((isCustomer ? salesforceUser.getUsername() + ".customer" : salesforceUser.getUsername()), guid);

                log.info("User Password GUID posted to UMP");

                //  Compute password from generated guid and obtain user_nonce in SHA-256
                final String password = Hashing.sha256()
                        .hashString(userNonce + guid, StandardCharsets.UTF_8)
                        .toString();

                //  Create UMP user
                log.info("Creating UMP User");
                umpUser = createUMPUser(salesforceUser, password, isCustomer);

                if (umpUser == null) {
                    throw new Exception("Unable to create User in UMP");
                }

                log.info("UMP User created");
                umpUser.setPassword(password);

            } else {
                log.info("UMP user found");

                final String s = new ObjectMapper().writeValueAsString(user);
                umpUser = new ObjectMapper().readValue(s, UMPUser.class);

                final String username = isCustomer ? salesforceUser.getUsername() + ".customer" : salesforceUser.getUsername();

                if (!username.equalsIgnoreCase(umpUser.getUserName())) {
                    throw new Exception("This email is associated with a different account type, check with your Administrator");
                }

                //  Stop if UMP User is disabled
                if (Constants.DISABLED.equals(umpUser.getStatus())) {
                    throw new Exception("User has been disabled");
                }

                //  Recompute the password
                //  Check user_nonce to compute password
                if (userNonce == null || userNonce.isEmpty()) {
                    log.error("user_nonce value is not set");
                    throw new Exception("Company configuration is incorrect, report to your Administrator");
                }

                //  Get user GUID from UMP
                final String guid = getUserGUID(umpUser.getUserName());

                if (isNullOrEmpty(guid)) {
                    log.info("User Password GUID is null");

                    //  Check user_nonce to compute password
                    throw new Exception("User is configured incorrectly, report to your Administrator");
                }

                //  Compute password from generated guid and obtain user_nonce in SHA-256
                final String password = Hashing.sha256()
                        .hashString(userNonce + guid, StandardCharsets.UTF_8)
                        .toString();

                umpUser.setPassword(password);

            }

            //Check frontend Id
            checkAndCreateFrontendIds(umpUser);

            //  Deploy App to frontend users
            log.info("Deploying App to frontend users");
            deployAppToUser(umpUser);
            log.info("App deployed to frontend users");

            //Set User Settings
            log.info("Posting Auth token and Refresh token to App settings");
            setAppUserSetting(tokenInfo, umpUser);
            log.info("Posted");

            //  Create or Update Backend user if dose not exist
            log.info("Create/Update Backend user");
            createOrUpdateBackendUser(tokenInfo.getAccess_token(), umpUser);

            //  Create a json with data required
            final JsonObject finalDataJson = new JsonObject();
            finalDataJson.addProperty(Constants.USERNAME, umpUser.getUserName());
            finalDataJson.addProperty(Constants.EMAIL, umpUser.getEmail());
            finalDataJson.addProperty(Constants.PASSWORD, umpUser.getPassword());

            //  Ass more details if its connect app
            if (isConnect) {
                final String token = getJWToken(umpUser);

                finalDataJson.addProperty(Constants.REDIRECT, redirect);
                finalDataJson.addProperty(Constants.TOKEN, token);
                finalDataJson.addProperty(Constants.UMP_URL, umpUrl);
            }

            //  Encode data
            final String encodedData = Base64.getEncoder().encodeToString(finalDataJson.toString().getBytes());

            log.info("Send UMP user details to " + (isConnect ? "Connect" : "device"));
            final UriComponents components = UriComponentsBuilder.newInstance()
                    .host(isConnect ? webappRedirectUrl : deviceRedirectUrl)
                    .queryParam(Constants.DATA, encodedData).build();

            String finalUrl = components.toUriString();
            // If we are handling new device logic then use the redir url without the https and instead use samson://
            if (!isConnect && !Constants.REDIRECT.equalsIgnoreCase(redirect)) {
                log.info("New device redirect found");
                finalUrl = finalUrl.replace("https://", "");
            }

            log.info("Redirecting to {}", finalUrl);
            httpServletResponse.setHeader(Constants.LOCATION, finalUrl);
            httpServletResponse.setStatus(302);
            return;
        } catch (Exception e) {
            e.printStackTrace();
            error = e.getMessage();
            System.err.println(error);
        }

        log.info("Sending error details to " + (isConnect ? "Connect" : "device"));
        final UriComponents components = UriComponentsBuilder.newInstance()
                .host(isConnect ? webappRedirectUrl : deviceRedirectUrl)
                .queryParam(Constants.ERROR, error).build();

        httpServletResponse.setHeader(Constants.LOCATION, components.toUriString());
        httpServletResponse.setStatus(302);
    }

    private TokenInfo getToken(final String code, final boolean isCustomer) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_XML));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        final MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
        params.add("grant_type", "authorization_code");
        params.add(Constants.CLIENT_ID, clientId);
        params.add("client_secret", clientSecret);
        params.add(Constants.CODE, code);
        params.add(Constants.REDIRECT_URI, sfRedirect);

//        try {
//            Object a = getRestTemplate().postForObject("https://" + (isCustomer ? sfHostCustomer : sfHost) + URLHelper.sfToken, params, Object.class);
//            System.out.println("********* Test ***********" + new Gson().toJson(a));
//
//            return new Gson().fromJson(new Gson().toJson(a), TokenInfo.class);
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
        return getRestTemplate().postForObject("https://" + (isCustomer ? sfHostCustomer : sfHost) + URLHelper.sfToken, params, TokenInfo.class);
    }

    private SalesforceUser getUserInfo(final TokenInfo tokenInfo) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(tokenInfo.getAccess_token());

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        final ResponseEntity<SalesforceUser> response = getRestTemplate().exchange(tokenInfo.getId(), HttpMethod.GET, request, SalesforceUser.class);

        return response.getBody();
    }

    private ResponseEntity<Map> checkUMPUser(final SalesforceUser salesforceUser) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\SA", saPassword);

        final HttpEntity<String> request = new HttpEntity<>("", headers);


        String url = umpUrl + URLHelper.umpUsers;
        url = url.replace(URLHelper.USER_NAME, salesforceUser.getEmail());

        try {
            return getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    private UMPUser createUMPUser(final SalesforceUser salesforceUser, final String password, final boolean isCustomer) throws Exception {

        String username = isCustomer ? salesforceUser.getUsername() + ".customer" : salesforceUser.getUsername();

        final UMPUser umpUser = getNewUMPUser(salesforceUser, username);

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\SA", saPassword);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("application", applicationName);
        params.add("userType", "APPLICATION");
        params.add("verifyEmail", false);
        params.add("strict", false);

        params.add("mailId", umpUser.getEmail());
        params.add("userName", umpUser.getUserName());
        params.add("password", password);

        //  Set default First Name if not provided by SalesForce
        if (!isNullOrEmpty(umpUser.getFirstName())) {
            params.add("firstName", umpUser.getFirstName());
        } else {
            params.add("firstName", "FName");
        }

        //  Set default Last Name if not provided by SalesForce
        if (!isNullOrEmpty(umpUser.getLastName())) {
            params.add("lastName", umpUser.getLastName());
        } else {
            params.add("lastName", "LName");
        }


        final JsonObject androidFEId = new JsonObject();
        androidFEId.addProperty("frontend", "ANDROID_PHONE");
        androidFEId.addProperty("frontendType", "ANDROID_PHONE");
        androidFEId.addProperty("frontendUser", username + "_droid");

        final JsonObject iPhoneFEId = new JsonObject();
        iPhoneFEId.addProperty("frontend", "IPHONE");
        iPhoneFEId.addProperty("frontendType", "IPHONE");
        iPhoneFEId.addProperty("frontendUser", username + "_iphone");

        final JsonObject iPadFEId = new JsonObject();
        iPadFEId.addProperty("frontend", "IPAD");
        iPadFEId.addProperty("frontendType", "IPAD");
        iPadFEId.addProperty("frontendUser", username + "_ipad");

        final JsonArray frontEndData = new JsonArray();
        frontEndData.add(androidFEId);
        frontEndData.add(iPhoneFEId);
        frontEndData.add(iPadFEId);

        params.add("frontendUsers", frontEndData);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLHelper.umpCreateUser;
        url = url.replace(URLHelper.USER_NAME, username);

        final Map response = getRestTemplate().postForObject(url, request, Map.class);

        final String error = String.valueOf(response.get("error"));
        final String message = String.valueOf(response.get("message"));

        if ((isNullOrEmpty(error) || "null".equals(error))
                && (!isNullOrEmpty(message) || !"null".equals(message))) {
            umpUser.setStatus(Constants.ENABLED);
            return umpUser;
        }

        //  Throw error details in case user creation fails
        final JsonObject finalDataJson = new JsonObject();
        finalDataJson.addProperty("error", error);
        finalDataJson.addProperty("message", message);

        System.err.println("Error while creating UMP User: " + finalDataJson.toString());

        throw new Exception(error);
    }

    private UMPUser getNewUMPUser(final SalesforceUser salesforceUser, final String username) {

        final UMPUser umpUser = new UMPUser();
        umpUser.setUserName(username);
        umpUser.setFirstName(salesforceUser.getFirst_name());
        umpUser.setLastName(salesforceUser.getLast_name());

        umpUser.setEmail(salesforceUser.getEmail());


        final Map<String, String> android = new LinkedHashMap<>();
        android.put("frontend", "ANDROID_PHONE");
        android.put("frontendUser", username + "_droid");

        final Map<String, String> iphone = new LinkedHashMap<>();
        iphone.put("frontend", "IPHONE");
        iphone.put("frontendUser", username + "_iphone");

        final Map<String, String> ipad = new LinkedHashMap<>();
        ipad.put("frontend", "IPAD");
        ipad.put("frontendUser", username + "_ipad");

        umpUser.setFrontendUsers(new Map[]{android, iphone, ipad});

        return umpUser;
    }

    private void setAppUserSetting(final TokenInfo tokenInfo, final UMPUser umpUser) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\" + umpUser.getUserName(), umpUser.getPassword());

        final JsonObject sfdcToken = new JsonObject();
        sfdcToken.addProperty("authToken", tokenInfo.getAccess_token());
        sfdcToken.addProperty("refreshToken", tokenInfo.getRefresh_token());

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("value", sfdcToken);
        params.add("hidden", true);
        params.add("secure", true);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLHelper.umpUserSettings;
        url = url.replace(URLHelper.APP_NAME, applicationName);
        url = url.replace(URLHelper.KEY, "SFDC_TOKEN");

        getRestTemplate().postForObject(url, request, Map.class);
    }

    private void checkAndCreateFrontendIds(final UMPUser umpUser) {
        final List<String> feIdsToCreate = new ArrayList<>();
        feIdsToCreate.add("ANDROID_PHONE");
        feIdsToCreate.add("IPHONE");
        feIdsToCreate.add("IPAD");
        feIdsToCreate.add("WINDOWS8");
        feIdsToCreate.add("Web");
        if (umpUser.getFrontendUsers() != null && umpUser.getFrontendUsers().length > 0) {
            for (Map map : umpUser.getFrontendUsers()) {
                feIdsToCreate.remove(map.get("frontend").toString());
            }
        }

        //  Create frontend Ids if dose not exist
        for (String feId : feIdsToCreate) {
            final MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            switch (feId) {
                case "ANDROID_PHONE":
                    params.add("frontend", "ANDROID_PHONE");
                    params.add("frontendUser", umpUser.getUserName().toLowerCase() + "_droid");
                    log.info("Creating android frontend user");
                    break;

                case "IPHONE":
                    params.add("frontend", "IPHONE");
                    params.add("frontendUser", umpUser.getUserName().toLowerCase() + "_iphone");
                    log.info("Creating iPhone frontend user");
                    break;

                case "IPAD":
                    params.add("frontend", "IPAD");
                    params.add("frontendUser", umpUser.getUserName().toLowerCase() + "_ipad");
                    log.info("Creating iPad frontend user");
                    break;

                case "WINDOWS8":
                    params.add("frontend", "WINDOWS8");
                    params.add("frontendUser", umpUser.getUserName().toLowerCase() + "_windows8");
                    log.info("Creating Windows frontend user");
                    break;

                case "Web":
                    params.add("frontend", "Web");
                    params.add("frontendUser", umpUser.getUserName().toLowerCase() + "_web");
                    log.info("Creating Web frontend user");
                    break;
            }

            createFrontendIds(umpUser.getUserName(), params);
        }

    }

    private void createFrontendIds(final String username, final MultiValueMap<String, String> params) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\SA", saPassword);

        final HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<MultiValueMap<String, String>>(params, headers);

        String url = umpUrl + URLHelper.umpFrontendUser;
        url = url.replace(URLHelper.USER_NAME, username);

        //Create frontend Ids
        getRestTemplate().postForObject(url, request, Map.class);

    }

    private void createOrUpdateBackendUser(final String accessToken, final UMPUser umpUser) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\" + umpUser.getUserName(), umpUser.getPassword());

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
        params.add("backendUser", umpUser.getUserName());
        params.add("password", accessToken);
        params.add("portName", backendPort);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLHelper.umpBackendUser;
        url = url.replace(URLHelper.USER_NAME, umpUser.getUserName());

        //Update backend
        getRestTemplate().postForObject(url, request, Map.class);

    }

    private void deployAppToUser(final UMPUser umpUser) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\SA", saPassword);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLHelper.umpAppDeploy;
        url = url.replace(URLHelper.APP_NAME, applicationName);
        url = url.replace(URLHelper.FRONTEND_USER, umpUser.getUserName().toLowerCase() + "_droid");

        //Deploy Android Frontend User
        getRestTemplate().postForObject(url, request, Map.class);

        url = url.replace("_droid", "_iphone");

        //Deploy iPhone Frontend User
        getRestTemplate().postForObject(url, request, Map.class);

        url = url.replace("_iphone", "_ipad");

        //Deploy iPad Frontend User
        getRestTemplate().postForObject(url, request, Map.class);

        url = url.replace("_ipad", "_windows8");

        //Deploy Windows Frontend User
        getRestTemplate().postForObject(url, request, Map.class);

        url = url.replace("_windows8", "_web");

        //Deploy Web Frontend User
        getRestTemplate().postForObject(url, request, Map.class);

    }

    private String getUserGUID(final String userName) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\SA", saPassword);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLHelper.umpAppSettings;
        url = url.replace(URLHelper.APP_NAME, applicationName);
        url = url.replace(URLHelper.KEY, userName.toLowerCase());

        try {

            final ResponseEntity<Map> responseEntity = getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);

            if (responseEntity.getBody() != null && responseEntity.getBody().get("settings") != null) {
                ArrayList<Map> settings = (ArrayList<Map>) responseEntity.getBody().get("settings");

                for (Map setting : settings) {
                    if (!isNullOrEmpty(String.valueOf(setting.get("value")))
                            && !"null".equals(String.valueOf(setting.get("value")))) {
                        return (String) setting.get("value");
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    private void setUserGUID(final String username, final String guid) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\SA", saPassword);

        final MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("value", guid);
        params.add("hidden", true);
        params.add("secure", true);

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

        String url = umpUrl + URLHelper.umpAppSettings;
        url = url.replace(URLHelper.APP_NAME, applicationName);
        url = url.replace(URLHelper.KEY, username.toLowerCase());

        try {
            getRestTemplate().postForObject(url, request, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private String getJWToken(final UMPUser umpUser) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\" + umpUser.getUserName(), umpUser.getPassword());

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLHelper.umpSession;
        url = url.replace(URLHelper.APP_NAME, applicationName);

        try {

            final ResponseEntity<Map> responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, request, Map.class);

            if (responseEntity.getBody() != null && responseEntity.getBody().get(Constants.TOKEN) != null) {
                return (String) responseEntity.getBody().get(Constants.TOKEN);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    private RestTemplate getRestTemplate() {
        final RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(new HttpComponentsClientHttpRequestFactory());
        restTemplate.setErrorHandler(new ResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                return false;
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {

            }
        });

        return restTemplate;
    }

    private static boolean isNullOrEmpty(final String text) {
        return text == null || text.trim().isEmpty();
    }

    @GetMapping({"/getmodel/{fileName}", "ml/getmodel"})
    @CrossOrigin(origins = "*")
    public ResponseEntity<Resource> getModel(@RequestHeader(HttpHeaders.AUTHORIZATION) String basicAuthCreds,
                                             HttpServletRequest httpServletRequest,
                                             HttpServletResponse httpServletResponse,
                                             @PathVariable(value = "fileName", required = false) String fileName) {

        log.info("OAutController '/getmodel' called");
        log.info("Fetching the model");

        if(null == basicAuthCreds || basicAuthCreds.trim().equals("")) {

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }

        if(!validateToken(basicAuthCreds)){

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }

        String rootFolder = getValueFromAppSettings(Constants.PA_SETTINGS_SAMSON_MODEL_PATH);

        if(null == rootFolder || rootFolder.isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
        log.info("Configured Path --> " + rootFolder);

        Path filePath = Paths.get(rootFolder).resolve(fileName);

        try {
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists()) {
                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "POST,OPTIONS,GET,PUT,PATCH,DELETE");
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + resource.getFilename());
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, httpServletRequest.getHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN));
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "Origin, Authorization, Accept, Content-Type, X-Requested-With");
                headers.add("Access-Control-Expose-Headers", "*");
                headers.add("Access-Control-Allow-Credentials", "true");
                headers.add("Access-Control-Max-Age", "60");

                return ResponseEntity.ok()
                        .headers(headers)
                        .contentLength(resource.getFile().length())
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IOException e) {
            // Handle exception
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
//        return response;
    }

    @GetMapping({"/getmodel/{modelType}/{fileName}", "ml/getmodel"})
    @CrossOrigin(origins = "*")
    public ResponseEntity<Resource> getModel(@RequestHeader(HttpHeaders.AUTHORIZATION) String basicAuthCreds,
                                             HttpServletRequest httpServletRequest,
                                             HttpServletResponse httpServletResponse,
                                             @PathVariable(value = "modelType", required = false) String modelType,
                                             @PathVariable(value = "fileName", required = false) String fileName) {

        log.info("OAutController '/modelType/getmodel' called");

        log.info("Fetching the model");

        if(null == basicAuthCreds || basicAuthCreds.trim().equals("")) {

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }

        if(!validateToken(basicAuthCreds)){

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }

        String rootFolder = getValueFromAppSettings(Constants.PA_SETTINGS_SAMSON_MODEL_PATH);

        if(null == rootFolder || rootFolder.isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
        log.info("Configured Path --> " + rootFolder);

        Path filePath = Paths.get(rootFolder, modelType).resolve(fileName);

        try {
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists()) {
                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "POST,OPTIONS,GET,PUT,PATCH,DELETE");
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + resource.getFilename());
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, httpServletRequest.getHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN));
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "Origin, Authorization, Accept, Content-Type, X-Requested-With");
                headers.add("Access-Control-Expose-Headers", "*");
                headers.add("Access-Control-Allow-Credentials", "true");
                headers.add("Access-Control-Max-Age", "60");

                return ResponseEntity.ok()
                        .headers(headers)
                        .contentLength(resource.getFile().length())
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IOException e) {
            // Handle exception
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
//        return response;
    }

    private boolean validateToken(String basicAuthCreds) {

        log.info("OAutController validating token");

        String token = "";
        if(basicAuthCreds.startsWith("Bearer")) {
            token = basicAuthCreds.substring("Bearer".length()).trim();
            log.info("Samson OAuth - Using Bearer token");
        } else {
            token = basicAuthCreds.substring("Basic".length()).trim();

            log.info("Samson OAuth - Using Basic token");
        }

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        if(basicAuthCreds.startsWith("Bearer")) {

            headers.setBearerAuth(token);

        } else {
            try {
                byte[] decodedBytes = Base64.getDecoder().decode(token);
                String decodedString = new String(decodedBytes);

                // Split the string into username and password
                String[] credentials = decodedString.split(":", 2);
                if (credentials.length == 2) {
                    String username = credentials[0];
                    String password = credentials[1];
                    headers.setBasicAuth(username, password, StandardCharsets.UTF_8);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLHelper.umpUser;

        try {

            final ResponseEntity<Map> responseEntity = getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);

            return responseEntity.getStatusCode() == HttpStatus.OK;

        }catch (Exception e){
            log.error("OAutController validating token error -> "+ e.getMessage());
        }

        return false;
    }

    private String getValueFromAppSettings(String key){

        final HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth(company + "\\SA", saPassword);

        final HttpEntity<String> request = new HttpEntity<String>("", headers);

        String url = umpUrl + URLHelper.umpAppSettings;
        url = url.replace(URLHelper.APP_NAME, applicationName);
        url = url.replace(URLHelper.KEY, key);

        try {

            final ResponseEntity<Map> responseEntity = getRestTemplate().exchange(url, HttpMethod.GET, request, Map.class);

            if (responseEntity.getBody() != null && responseEntity.getBody().get("settings") != null) {
                ArrayList<Map> settings = (ArrayList<Map>) responseEntity.getBody().get("settings");

                for (Map setting : settings) {
                    if (!isNullOrEmpty(String.valueOf(setting.get("value")))
                            && !"null".equals(String.valueOf(setting.get("value")))) {
                        return (String) setting.get("value");
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ".";
    }
}