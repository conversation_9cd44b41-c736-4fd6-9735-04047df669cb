package com.unvired.oauthonboarding;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

@SpringBootApplication
public class OauthOnboardingApplication extends SpringBootServletInitializer {
    private static final Class<OauthOnboardingApplication> applicationClass = OauthOnboardingApplication.class;

    public static void main(String[] args) {
        SpringApplication.run(OauthOnboardingApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }

}
