package com.unvired.oauthonboarding.helpers;

public class URLHelper {

    public final static String APP_NAME = "{appName}";
    public final static String USER_NAME = "{userName}";
    public final static String FRONTEND_USER = "{frontendUser}";
    public final static String KEY = "{key}";

    public final static String sfAuthorize = "/services/oauth2/authorize";
    public final static String sfToken = "/services/oauth2/token";

    public final static String umpUsers = "/API/v2/users/" + USER_NAME + "";
    public final static String umpCreateUser = "/API/v2/users/" + USER_NAME + "/provision";

    public final static String umpAppDeploy = "/API/v2/applications/" + APP_NAME + "/deploy/" + FRONTEND_USER;
    public final static String umpUserSettings = "/API/v2/applications/" + APP_NAME + "/usersettings/" + KEY;
    public final static String umpAppSettings = "/API/v2/applications/" + APP_NAME + "/settings/" + KEY;

    public final static String umpFrontendUser = umpUsers + "/frontendusers";
    public final static String umpBackendUser = umpUsers + "/backendusers";

    public final static String umpSession = "/API/v2/applications/"+ APP_NAME +"/session";
    public final static String umpUser = "/API/v2/users/:me";
}
