package com.unvired.oauthonboarding.helpers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.unvired.oauthonboarding.OAuthController;

public class Version {
    private static String version = "@VERSION@";
    private static String build = "@BUILD@";

    public static String getVersion() {
        Logger logger = LoggerFactory.getLogger(Version.class);
        logger.info("Version {}", version);
        return "Provisioning - " + version + " : " + build;
    }    
}
