package com.unvired.oauthonboarding.helpers;

import lombok.extern.slf4j.Slf4j;
import com.unvired.oauthonboarding.OAuthController;
@Slf4j
public class Version {
    private static String version = "@VERSION@";
    private static String build = "@BUILD@";

    public static String getVersion() {
        log.info("Version {}", version);
        return "Provisioning - " + version + " : " + build;
    }    
}
