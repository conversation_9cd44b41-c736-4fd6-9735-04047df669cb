package com.unvired.oauthonboarding.helpers;

public class Constants {
    public final static String CODE = "code";
    public final static String CUSTOMER = "customer";
    public final static String EMPLOYEE = "employee";
    public final static String CONNECT = "connect";
    public final static String OAUTH = "OAuth";
    public final static String ROLE = "role";
    public final static String SOURCE = "source";
    public final static String REDIRECT = "redirect";
    public final static String STATE = "state";

    public final static String DELIMITER = "~~";
    public final static String SCHEME = "https";
    public final static String RESPONSE_TYPE = "response_type";
    public final static String CLIENT_ID = "client_id";
    public final static String REDIRECT_URI = "redirect_uri";


    public final static String LOCATION = "Location";
    public final static String DATA = "data";
    public final static String ERROR = "error";
    public final static String USER = "user";
    public final static String USERNAME = "UserName";
    public final static String EMAIL = "Email";
    public final static String PASSWORD = "Password";
    public final static String TOKEN = "token";
    public final static String UMP_URL = "ump_url";

    public final static String ENABLED = "ENABLED";
    public final static String DISABLED = "DISABLED";

    public final static String PA_SETTINGS_SAMSON_MODEL_PATH = "SAMSON_MODEL_PATH";
}
